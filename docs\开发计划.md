# Windows硬件工具箱 - 渐进式开发计划

## 📋 项目概述

**项目名称**: Windows硬件工具箱  
**开发模式**: 渐进式开发（方案一）  
**技术栈**: PySide6 + PySide6-Fluent-Widgets  
**设计规范**: Microsoft Fluent Design + Windows 11  
**开发周期**: 21-28天  
**文档创建时间**: 2025-07-24T15:40:00+08:00

## 🏗️ 技术架构设计

### 核心架构
```
Windows硬件工具箱
├── 表现层 (UI Layer)
│   ├── MSFluentWindow (主窗口框架)
│   ├── NavigationInterface (导航系统)
│   └── 6个功能视图 (Views)
├── 业务逻辑层 (Core Layer)
│   ├── 硬件检测模块
│   ├── 系统优化模块
│   ├── 应用管理模块
│   └── 工具管理模块
├── 工具层 (Utils Layer)
│   ├── 权限管理
│   ├── 图标提取
│   └── 进程管理
└── 配置层 (Config Layer)
    ├── 系统设置
    └── 功能配置
```

### 关键技术选型
- **主窗口**: MSFluentWindow (支持更丰富功能)
- **硬件检测**: WMI + psutil 双重保障
- **权限管理**: ctypes.windll.shell32.IsUserAnAdmin()
- **主题系统**: SystemThemeListener 自动跟随系统
- **异步处理**: QThread 避免界面卡顿

## 🚀 渐进式开发计划

### 阶段1：基础架构搭建 (2-3天)

**目标**: 建立可运行的应用框架

**核心任务**:
1. **管理员权限检查模块**
   - 创建 `utils/admin_check.py`
   - 实现权限检查和UAC提升
   - 非管理员模式的友好提示

2. **主窗口框架**
   - 创建 `ui/main_window.py`
   - 实现MSFluentWindow基础结构
   - 配置左侧导航栏

3. **项目结构完善**
   - 创建所有必要的目录
   - 建立模块间的基础连接
   - 配置基础的错误处理

**技术实现**:
```python
# utils/admin_check.py 核心逻辑
import ctypes
import sys

def is_admin():
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def require_admin():
    if not is_admin():
        # 显示提示对话框并退出
        ctypes.windll.shell32.ShellExecuteW(
            None, "runas", sys.executable, " ".join(sys.argv), None, 1
        )
        sys.exit(0)
```

**验收标准**:
- ✅ 应用可以正常启动
- ✅ 管理员权限检查正常工作
- ✅ 基础导航栏显示正确
- ✅ 窗口大小和位置符合设计要求

### 阶段2：硬件信息模块 (3-4天)

**目标**: 完整的硬件信息检测和展示

**核心任务**:
1. **硬件检测核心**
   - 重构 `docs/部分思路解析.md` 中的代码
   - 创建 `core/hardware_detection.py`
   - 实现异步硬件检测 (QThread)

2. **硬件信息界面**
   - 创建 `ui/views/hardware_info.py`
   - 使用CardWidget卡片式布局
   - 实现信息复制和导出功能

3. **性能优化**
   - 启动时预加载硬件信息
   - 实现加载进度显示
   - 优化内存使用

**技术实现**:
```python
# core/hardware_detection.py 异步检测
from PySide6.QtCore import QThread, Signal

class HardwareDetectionThread(QThread):
    hardware_detected = Signal(dict)
    
    def run(self):
        # 执行硬件检测逻辑
        hardware_info = self.get_all_hardware_info()
        self.hardware_detected.emit(hardware_info)
```

**验收标准**:
- ✅ 硬件信息检测准确完整
- ✅ 界面响应流畅，无卡顿
- ✅ 信息复制和导出功能正常
- ✅ 错误处理机制完善

### 阶段3：快捷工具模块 (2-3天)

**目标**: 系统工具快速访问功能

**核心任务**:
1. **配置解析**
   - 解析 `config/quick_tools.py`
   - 实现工具分类和展示逻辑

2. **工具界面**
   - 创建 `ui/views/quick_tools.py`
   - 使用FlowLayout分类展示
   - 实现工具启动按钮

3. **启动逻辑**
   - 实现外部程序启动
   - 添加二次确认机制
   - 处理启动失败情况

**验收标准**:
- ✅ 所有快捷工具正常启动
- ✅ 分类展示清晰美观
- ✅ 危险操作有确认提示
- ✅ 启动失败有友好提示

### 阶段4：系统优化模块 (4-5天)

**目标**: 核心的系统优化功能

**核心任务**:
1. **PowerShell优化**
   - 解析 `config/powershell_commands.py`
   - 实现PowerShell命令执行
   - 添加执行进度显示

2. **注册表优化**
   - 解析 `config/registry_commands.py`
   - 实现注册表操作
   - 树形结构展示父子关系

3. **系统清理**
   - 解析 `config/system_cleanup.py`
   - 实现各种清理功能
   - 批量操作和进度反馈

**验收标准**:
- ✅ 所有优化功能正常执行
- ✅ 批量操作界面友好
- ✅ 执行进度实时反馈
- ✅ 执行后自动重启资源管理器

### 阶段5：预装应用模块 (4-5天)

**目标**: UWP应用管理和OneDrive清理

**核心任务**:
1. **UWP应用检测**
   - 使用PowerShell Get-AppxPackage
   - 实现应用分类展示
   - 添加应用信息显示

2. **应用卸载逻辑**
   - 实现Remove-AppxPackage
   - 通配符查找和卸载
   - 批量卸载功能

3. **OneDrive完整清理**
   - 解析 `config/onedrive_cleanup.py`
   - 实现完整的清理流程
   - 特殊的安全确认机制

**验收标准**:
- ✅ 应用检测准确完整
- ✅ 卸载功能安全可靠
- ✅ OneDrive清理彻底
- ✅ 操作前有充分警告

### 阶段6：超频工具模块 (3-4天)

**目标**: 第三方超频工具管理

**核心任务**:
1. **文件夹扫描**
   - 扫描OCTools文件夹结构
   - 识别可执行文件
   - 处理文件夹不存在情况

2. **图标提取和展示**
   - 使用 `utils/icon_extractor.py`
   - 实现工具卡片展示
   - 网格布局优化

3. **工具启动管理**
   - 区分GUI/Console程序
   - 以管理员权限启动
   - BIOS重启功能

**验收标准**:
- ✅ 工具扫描准确无误
- ✅ 图标提取正常显示
- ✅ 工具启动功能完善
- ✅ BIOS重启安全可靠

### 阶段7：界面完善 (3-4天)

**目标**: 产品级用户体验

**核心任务**:
1. **启动动画**
   - 创建 `splash.py`
   - 实现SplashScreen
   - 预加载硬件信息

2. **主题切换系统**
   - 实现SystemThemeListener
   - 明暗主题自动切换
   - 主题设置保存

3. **设置关于页面**
   - 创建 `ui/views/settings_about.py`
   - 应用信息和版本显示
   - 支付二维码和链接

**验收标准**:
- ✅ 启动动画流畅美观
- ✅ 主题切换功能完善
- ✅ 设置页面信息完整
- ✅ 整体用户体验优秀

## 🛡️ 风险控制和测试策略

### 风险控制措施
1. **权限风险**: 最小化管理员权限使用
2. **系统风险**: 关键操作前用户确认
3. **数据风险**: 重要操作提供回滚机制
4. **兼容性风险**: 多Windows版本测试

### 测试策略
1. **单元测试**: 核心功能模块测试
2. **集成测试**: 模块间交互测试
3. **用户测试**: 界面操作流程测试
4. **兼容性测试**: Windows 10/11测试

## 📊 里程碑和时间规划

| 阶段 | 里程碑 | 预计时间 | 关键交付物 |
|------|--------|----------|------------|
| 1 | 基础框架完成 | 2-3天 | 可启动的应用框架 |
| 2 | 硬件信息完成 | 3-4天 | 完整硬件信息展示 |
| 3 | 快捷工具完成 | 2-3天 | 系统工具快速访问 |
| 4 | 系统优化完成 | 4-5天 | 核心优化功能 |
| 5 | 应用管理完成 | 4-5天 | UWP应用管理 |
| 6 | 超频工具完成 | 3-4天 | 第三方工具集成 |
| 7 | 产品完成 | 3-4天 | 完整产品体验 |

**总开发周期**: 21-28天

## 📝 开发规范

### 代码规范
- 严格遵循PEP 8编码规范
- 使用类型提示 (Type Hints)
- 详细的文档字符串 (Docstrings)
- 统一的命名约定

### 文档管理
- 及时更新README.md
- 记录开发日志到logs.md
- 维护requirements.txt
- 更新版本信息

### 版本控制
- 每个阶段创建独立分支
- 重要节点打标签
- 详细的提交信息

## 🔧 技术实现细节

### 核心模块设计

#### 1. 主窗口架构 (ui/main_window.py)
```python
from qfluentwidgets import MSFluentWindow, NavigationItemPosition
from qfluentwidgets import FluentIcon as FIF

class MainWindow(MSFluentWindow):
    def __init__(self):
        super().__init__()
        self.initNavigation()
        self.initWindow()

    def initNavigation(self):
        # 添加6个主要功能界面
        self.addSubInterface(self.hardwareInterface, FIF.IOT, '硬件信息')
        self.addSubInterface(self.optimizationInterface, FIF.BROOM, '优化清理')
        self.addSubInterface(self.appsInterface, FIF.APPLICATION, '预装应用')
        self.addSubInterface(self.overclockInterface, FIF.SPEED_HIGH, '超频工具')
        self.addSubInterface(self.quickToolsInterface, FIF.QUICK_NOTE, '快捷工具')
        self.addSubInterface(self.settingsInterface, FIF.SETTING, '设置关于',
                           position=NavigationItemPosition.BOTTOM)
```

#### 2. 异步硬件检测 (core/hardware_detection.py)
```python
from PySide6.QtCore import QThread, Signal
import wmi
import psutil

class HardwareDetectionThread(QThread):
    hardware_detected = Signal(dict)
    progress_updated = Signal(str, int)  # 状态, 进度

    def run(self):
        try:
            self.progress_updated.emit("正在检测CPU信息...", 10)
            cpu_info = self.get_cpu_info()

            self.progress_updated.emit("正在检测内存信息...", 30)
            memory_info = self.get_memory_info()

            self.progress_updated.emit("正在检测显卡信息...", 50)
            gpu_info = self.get_gpu_info()

            # ... 其他硬件检测

            hardware_data = {
                'cpu': cpu_info,
                'memory': memory_info,
                'gpu': gpu_info,
                # ...
            }

            self.progress_updated.emit("检测完成", 100)
            self.hardware_detected.emit(hardware_data)

        except Exception as e:
            self.error_occurred.emit(str(e))
```

#### 3. 系统操作封装 (core/system_operations.py)
```python
import subprocess
import winreg
from typing import List, Dict, Optional

class SystemOperations:
    @staticmethod
    def execute_powershell(command: str) -> Dict[str, any]:
        """执行PowerShell命令"""
        try:
            result = subprocess.run(
                ["powershell", "-Command", command],
                capture_output=True,
                text=True,
                check=True
            )
            return {"success": True, "output": result.stdout}
        except subprocess.CalledProcessError as e:
            return {"success": False, "error": e.stderr}

    @staticmethod
    def modify_registry(key_path: str, value_name: str, value_data: any) -> bool:
        """修改注册表"""
        try:
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path, 0,
                              winreg.KEY_SET_VALUE) as key:
                winreg.SetValueEx(key, value_name, 0, winreg.REG_DWORD, value_data)
            return True
        except Exception as e:
            print(f"注册表修改失败: {e}")
            return False
```

### 界面组件设计

#### 1. 硬件信息卡片 (ui/components/hardware_card.py)
```python
from qfluentwidgets import CardWidget, BodyLabel, CaptionLabel
from PySide6.QtWidgets import QVBoxLayout, QHBoxLayout

class HardwareCard(CardWidget):
    def __init__(self, title: str, info_dict: Dict[str, str], parent=None):
        super().__init__(parent)
        self.setupUI(title, info_dict)

    def setupUI(self, title: str, info_dict: Dict[str, str]):
        self.titleLabel = BodyLabel(title)
        self.vBoxLayout = QVBoxLayout(self)

        self.vBoxLayout.addWidget(self.titleLabel)

        for key, value in info_dict.items():
            info_layout = QHBoxLayout()
            key_label = CaptionLabel(f"{key}:")
            value_label = BodyLabel(value)

            info_layout.addWidget(key_label)
            info_layout.addWidget(value_label)
            self.vBoxLayout.addLayout(info_layout)
```

#### 2. 工具按钮组件 (ui/components/tool_button.py)
```python
from qfluentwidgets import PrimaryPushButton, MessageBox
from PySide6.QtCore import Signal

class ToolButton(PrimaryPushButton):
    tool_executed = Signal(str, bool)  # 工具名, 是否成功

    def __init__(self, tool_config: Dict, parent=None):
        super().__init__(tool_config['name'], parent)
        self.tool_config = tool_config
        self.clicked.connect(self.execute_tool)

    def execute_tool(self):
        if self.tool_config.get('require_confirm', False):
            if not self.show_confirmation():
                return

        # 执行工具逻辑
        success = self.run_tool_command()
        self.tool_executed.emit(self.tool_config['name'], success)

    def show_confirmation(self) -> bool:
        dialog = MessageBox(
            "确认操作",
            f"确定要执行 {self.tool_config['name']} 吗？",
            self.parent()
        )
        return dialog.exec()
```

## 📋 详细任务清单

### 阶段1任务清单
- [ ] 创建utils/admin_check.py模块
- [ ] 实现管理员权限检查函数
- [ ] 创建权限提升对话框
- [ ] 创建ui/main_window.py主窗口类
- [ ] 配置MSFluentWindow基础设置
- [ ] 实现基础导航栏结构
- [ ] 创建6个空白子界面
- [ ] 配置窗口图标和标题
- [ ] 实现基础错误处理机制
- [ ] 测试应用启动和权限检查

### 阶段2任务清单
- [ ] 重构硬件检测代码到core/hardware_detection.py
- [ ] 实现HardwareDetectionThread异步类
- [ ] 创建ui/views/hardware_info.py界面
- [ ] 设计硬件信息卡片组件
- [ ] 实现信息复制到剪贴板功能
- [ ] 添加信息导出为JSON功能
- [ ] 实现加载进度显示
- [ ] 优化硬件检测性能
- [ ] 添加硬件检测错误处理
- [ ] 测试硬件信息准确性

### 阶段3任务清单
- [ ] 解析config/quick_tools.py配置文件
- [ ] 创建ui/views/quick_tools.py界面
- [ ] 实现工具分类展示逻辑
- [ ] 创建ToolButton组件
- [ ] 实现工具启动功能
- [ ] 添加危险操作确认对话框
- [ ] 实现工具启动失败处理
- [ ] 优化工具按钮布局
- [ ] 添加工具描述和图标
- [ ] 测试所有快捷工具功能

## 🔍 质量保证措施

### 代码质量
1. **代码审查**: 每个模块完成后进行代码审查
2. **类型检查**: 使用mypy进行静态类型检查
3. **代码格式化**: 使用black自动格式化代码
4. **文档完整性**: 确保所有公共方法都有文档字符串

### 测试覆盖
1. **单元测试**: 核心业务逻辑100%覆盖
2. **集成测试**: 主要功能流程测试
3. **UI测试**: 界面操作和响应测试
4. **性能测试**: 启动速度和响应时间测试

### 用户体验
1. **响应性**: 所有操作响应时间<2秒
2. **友好性**: 错误信息用户友好
3. **一致性**: 界面风格和交互一致
4. **可访问性**: 支持键盘导航

## 📚 参考资料

### 技术文档
- [PySide6-Fluent-Widgets官方文档](https://qfluentwidgets.com/)
- [Microsoft Fluent Design System](https://learn.microsoft.com/zh-cn/windows/apps/design/)
- [Windows WMI参考](https://docs.microsoft.com/en-us/windows/win32/wmisdk/)
- [Python psutil文档](https://psutil.readthedocs.io/)

### 设计参考
- Windows 11设置应用界面
- Microsoft Store应用设计
- Visual Studio Code界面设计

---

**文档版本**: v1.0
**最后更新**: 2025-07-24T15:40:00+08:00
**创建者**: AI编程助手 🐾
