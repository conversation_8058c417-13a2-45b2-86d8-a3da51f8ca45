#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
预装应用视图模块
Windows预装应用管理功能

此模块将在阶段5中完整实现
"""

from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel
from PySide6.QtCore import Qt


class PreinstalledAppsView(QWidget):
    """预装应用视图类（占位符实现）"""

    def __init__(self, parent=None):
        super().__init__(parent=parent)
        self.setObjectName("PreinstalledAppsView")
        self.init_ui()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)

        # 标题
        title = SubtitleLabel("📱 预装应用", self)
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 描述
        description = BodyLabel("卸载Windows系统预装的应用程序", self)
        description.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 占位符内容
        placeholder = BodyLabel(
            "此功能将在阶段5中实现\n\n将包含以下功能：\n• Xbox应用管理\n• 商店应用管理\n• 编解码器应用\n• 系统应用管理\n• OneDrive完整清理",
            self,
        )
        placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)

        layout.addWidget(title)
        layout.addWidget(description)
        layout.addStretch()
        layout.addWidget(placeholder)
        layout.addStretch()

        self.setLayout(layout)
