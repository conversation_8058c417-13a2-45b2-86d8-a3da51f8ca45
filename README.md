# Windows硬件工具箱

一个基于PySide6和Fluent Design的现代化Windows系统工具，提供硬件信息检测、系统优化、应用管理等功能。

## 🚀 项目特色

- **现代化界面**: 遵循Microsoft Fluent Design设计规范
- **功能丰富**: 6大核心功能模块，满足不同需求
- **安全可靠**: 完善的权限管理和操作确认机制
- **高性能**: 异步处理，界面响应流畅
- **易于使用**: 直观的用户界面和操作流程

## 📋 功能模块

### 🔧 硬件信息
- CPU、内存、显卡、存储、主板等硬件详细信息
- 支持信息复制和导出
- 实时硬件状态监控

### 🧹 优化清理
- **PowerShell优化**: 执行策略设置、电源选项解锁
- **注册表优化**: 系统性能和界面优化
- **系统清理**: 临时文件、缓存、日志等清理

### 📱 预装应用
- Windows预装应用批量卸载
- Xbox、商店、编解码器等分类管理
- OneDrive完整清理功能

### ⚡ 超频工具
- 第三方超频工具集成管理
- 自动扫描和图标提取
- 一键进入BIOS功能

### 🚀 快捷工具
- 常用系统工具快速访问
- 电源管理（重启、关机、睡眠等）
- 网络工具和系统管理

### ⚙️ 设置关于
- 主题切换（明暗模式）
- 应用信息和版本管理
- 支持作者和社交链接

## 🛠️ 技术栈

- **UI框架**: PySide6 + PySide6-Fluent-Widgets
- **设计规范**: Microsoft Fluent Design + Windows 11
- **硬件检测**: WMI + psutil
- **系统操作**: PowerShell + Windows Registry
- **权限管理**: Windows UAC + ctypes

## 📦 安装要求

### 系统要求
- Windows 10/11 (64位)
- 管理员权限
- .NET Framework 4.7.2+

### Python依赖
```bash
pip install -r requirements.txt
```

主要依赖包：
- PySide6
- PySide6-Fluent-Widgets[full]
- psutil
- wmi
- pywin32

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone [项目地址]
cd Windows轻松优化工具
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 运行应用
```bash
python main.py
```

**注意**: 应用必须以管理员权限运行才能正常使用所有功能。

## 📁 项目结构

```
Windows轻松优化工具/
├── main.py                    # 主程序入口
├── splash.py                  # 启动动画
├── config/                    # 配置文件
│   ├── settings.py           # 应用设置
│   ├── constants.py          # 常量定义
│   ├── powershell_commands.py # PowerShell命令配置
│   ├── registry_commands.py   # 注册表操作配置
│   ├── system_cleanup.py      # 系统清理配置
│   ├── appx_packages.py       # 预装应用配置
│   ├── onedrive_cleanup.py    # OneDrive清理配置
│   └── quick_tools.py         # 快捷工具配置
├── ui/                        # 用户界面
│   ├── main_window.py        # 主窗口
│   ├── components/           # 公共组件
│   └── views/                # 功能视图
│       ├── hardware_info.py
│       ├── optimization.py
│       ├── preinstalled_apps.py
│       ├── overclock_tools.py
│       ├── quick_tools.py
│       └── settings_about.py
├── core/                      # 核心业务逻辑
│   ├── hardware_detection.py # 硬件信息获取
│   ├── system_operations.py  # 系统操作封装
│   └── ...
├── utils/                     # 工具模块
│   ├── admin_check.py        # 管理员权限检查
│   ├── icon_extractor.py     # 图标提取工具
│   └── ...
├── assets/                    # 资源文件
│   ├── icon.ico              # 应用图标
│   ├── alipay.png           # 支付宝二维码
│   └── wechat_pay.png       # 微信支付二维码
├── OCTools/                   # 超频工具目录
├── docs/                      # 文档
│   ├── 开发需求.md           # 需求文档
│   ├── 开发计划.md           # 开发计划
│   └── 部分思路解析.md       # 技术解析
├── requirements.txt           # Python依赖
├── logs.md                   # 开发日志
└── README.md                 # 项目说明
```

## 🔧 开发指南

### 开发环境设置
1. Python 3.8+
2. 推荐使用虚拟环境
3. 安装开发依赖

### 代码规范
- 遵循PEP 8编码规范
- 使用类型提示 (Type Hints)
- 详细的文档字符串 (Docstrings)

### 开发流程
详细的开发计划请参考 [docs/开发计划.md](docs/开发计划.md)

## 📝 更新日志

详细的开发日志请参考 [logs.md](logs.md)

## 🤝 贡献指南

欢迎提交Issue和Pull Request来帮助改进项目！

## 📄 许可证

本项目采用 MIT 许可证 - 详情请参阅 [LICENSE](LICENSE) 文件

## 👨‍💻 作者

**@抖音小念（凡尘超频团队）**

- 抖音主页: [链接]
- 官方Q群: [群号]

## 💝 支持项目

如果这个项目对您有帮助，欢迎：
- ⭐ 给项目点个Star
- 🐛 报告Bug和建议
- 💰 赞助支持开发

---

**最后更新**: 2025-07-24T15:46:41+08:00
