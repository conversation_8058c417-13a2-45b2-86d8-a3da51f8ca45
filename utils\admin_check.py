#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
管理员权限检查模块
提供Windows管理员权限检查和UAC提升功能

此模块用于确保应用程序以管理员权限运行，
这是执行系统级操作（如注册表修改、系统清理等）的必要条件。
"""

import ctypes
import sys
import os
from typing import Dict, Any
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import QCoreApplication
from PySide6.QtGui import QIcon


def is_admin() -> bool:
    """
    检查当前进程是否具有管理员权限

    Returns:
        bool: 如果具有管理员权限返回True，否则返回False

    Note:
        此函数使用Windows API来检查当前用户的权限级别
        在非Windows系统上会返回False
    """
    try:
        # 使用Windows API检查管理员权限
        return ctypes.windll.shell32.IsUserAnAdmin() != 0
    except AttributeError:
        # 非Windows系统
        return False
    except Exception as e:
        print(f"权限检查失败: {e}")
        return False


def show_admin_required_dialog() -> None:
    """
    显示需要管理员权限的对话框

    当检测到应用程序未以管理员权限运行时，
    显示友好的提示对话框告知用户需要管理员权限
    """
    # 确保QApplication已经创建
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)

    # 创建消息对话框
    msg_box = QMessageBox()
    msg_box.setWindowTitle("需要管理员权限")
    msg_box.setIcon(QMessageBox.Icon.Warning)

    # 设置对话框内容
    msg_box.setText("Windows硬件工具箱需要管理员权限才能正常运行")
    msg_box.setInformativeText(
        "本应用需要执行以下操作，需要管理员权限：\n\n"
        "• 读取系统硬件信息\n"
        "• 修改系统注册表\n"
        "• 执行PowerShell命令\n"
        "• 管理系统应用程序\n"
        "• 清理系统文件\n\n"
        '请右键点击应用程序图标，选择"以管理员身份运行"。'
    )

    # 设置按钮
    msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
    msg_box.setDefaultButton(QMessageBox.StandardButton.Ok)

    # 尝试设置应用程序图标
    try:
        icon_path = os.path.join(
            os.path.dirname(os.path.dirname(__file__)), "assets", "icon.ico"
        )
        if os.path.exists(icon_path):
            msg_box.setWindowIcon(QIcon(icon_path))
    except Exception:
        pass  # 如果图标加载失败，继续执行

    # 显示对话框
    msg_box.exec()


def request_admin_privileges() -> bool:
    """
    请求管理员权限

    如果当前进程没有管理员权限，尝试以管理员权限重新启动应用程序

    Returns:
        bool: 如果成功请求到管理员权限或已经具有管理员权限返回True，
              否则返回False

    Note:
        此函数会尝试使用UAC提升权限，如果用户拒绝或失败，
        会显示友好的错误提示
    """
    if is_admin():
        return True

    try:
        # 尝试以管理员权限重新启动应用程序
        # 使用ShellExecuteW API调用UAC提升
        result = ctypes.windll.shell32.ShellExecuteW(
            None,  # hwnd: 父窗口句柄
            "runas",  # lpOperation: 操作类型（以管理员身份运行）
            sys.executable,  # lpFile: 要执行的程序
            " ".join(sys.argv),  # lpParameters: 命令行参数
            None,  # lpDirectory: 工作目录
            1,  # nShowCmd: 显示方式（SW_SHOWNORMAL）
        )

        # 如果UAC提升成功，result > 32
        if result > 32:
            # 成功启动新进程，退出当前进程
            sys.exit(0)
        else:
            # UAC提升失败
            return False

    except Exception as e:
        print(f"权限提升失败: {e}")
        return False


def require_admin() -> None:
    """
    强制要求管理员权限

    检查当前进程是否具有管理员权限，如果没有则尝试获取权限。
    如果无法获取权限，显示错误对话框并退出应用程序。

    这是应用程序启动时应该调用的主要函数。
    """
    if is_admin():
        # 已经具有管理员权限，继续执行
        return

    # 尝试请求管理员权限
    if request_admin_privileges():
        # 成功获取权限（实际上这里不会执行到，因为会重新启动进程）
        return

    # 无法获取管理员权限，显示错误对话框
    show_admin_required_dialog()

    # 退出应用程序
    sys.exit(1)


def get_admin_status_info() -> dict:
    """
    获取当前管理员权限状态信息

    Returns:
        dict: 包含权限状态信息的字典
        {
            'is_admin': bool,           # 是否具有管理员权限
            'user_name': str,           # 当前用户名
            'is_elevated': bool,        # 是否通过UAC提升
            'integrity_level': str      # 完整性级别
        }
    """
    status_info = {
        "is_admin": is_admin(),
        "user_name": os.getenv("USERNAME", "Unknown"),
        "is_elevated": False,
        "integrity_level": "Unknown",
    }

    try:
        # 检查是否通过UAC提升
        # 这里可以添加更详细的权限检查逻辑
        if status_info["is_admin"]:
            status_info["is_elevated"] = True
            status_info["integrity_level"] = "High"
        else:
            status_info["integrity_level"] = "Medium"

    except Exception as e:
        print(f"获取权限状态信息失败: {e}")

    return status_info


# 模块级别的便捷函数
def check_admin_on_import() -> None:
    """
    模块导入时自动检查管理员权限

    这个函数可以在模块导入时自动调用，
    确保应用程序在启动时就具有必要的权限
    """
    if not is_admin():
        print("警告: 应用程序未以管理员权限运行")
        print("某些功能可能无法正常工作")


if __name__ == "__main__":
    # 测试代码
    print("管理员权限检查模块测试")
    print("-" * 40)

    status = get_admin_status_info()
    print(f"当前用户: {status['user_name']}")
    print(f"管理员权限: {status['is_admin']}")
    print(f"UAC提升: {status['is_elevated']}")
    print(f"完整性级别: {status['integrity_level']}")

    if not status["is_admin"]:
        print("\n需要管理员权限才能运行此应用程序")
        print("请以管理员身份重新运行")
    else:
        print("\n✓ 具有管理员权限，可以正常运行")
