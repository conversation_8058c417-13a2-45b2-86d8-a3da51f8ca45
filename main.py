#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Windows硬件工具箱 - 主程序入口
一个基于PySide6和Fluent Design的现代化Windows系统工具

功能特色：
- 硬件信息检测
- 系统优化清理
- 预装应用管理
- 超频工具集成
- 快捷工具访问
- 现代化界面设计

作者: @抖音小念（凡尘超频团队）
版本: 1.0.0
"""

import sys
import os
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt, QTranslator, QLocale
from PySide6.QtGui import QFont

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.admin_check import require_admin
from ui.main_window import create_main_window


def setup_application() -> QApplication:
    """
    设置应用程序基础配置

    Returns:
        QApplication: 配置好的应用程序实例
    """
    # 创建应用程序实例
    app = QApplication(sys.argv)

    # 设置应用程序基本信息
    app.setApplicationName("Windows硬件工具箱")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("凡尘超频团队")
    app.setOrganizationDomain("douyin.com")

    # 设置应用程序属性
    app.setAttribute(Qt.ApplicationAttribute.AA_DontCreateNativeWidgetSiblings)

    # 设置默认字体
    font = QFont("Segoe UI", 9)
    app.setFont(font)

    return app


def main():
    """主函数"""
    try:
        # 检查管理员权限
        print("检查管理员权限...")
        require_admin()
        print("✓ 管理员权限检查通过")

        # 设置应用程序
        print("初始化应用程序...")
        app = setup_application()
        print("✓ 应用程序初始化完成")

        # 创建主窗口
        print("创建主窗口...")
        main_window = create_main_window()
        print("✓ 主窗口创建完成")

        # 显示主窗口
        main_window.show()
        print("✓ 应用程序启动成功")

        # 运行应用程序
        sys.exit(app.exec())

    except KeyboardInterrupt:
        print("\n用户中断程序")
        sys.exit(0)

    except Exception as e:
        print(f"应用程序启动失败: {e}")
        import traceback

        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
