#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
硬件信息卡片组件
用于显示各种硬件信息的卡片式组件

提供统一的硬件信息展示界面，支持复制和导出功能
"""

import json
from typing import Dict, Any, List, Optional
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
    QPushButton, QScrollArea, QFrame, QApplication,
    QMessageBox, QFileDialog
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QClipboard


class InfoRow(QFrame):
    """信息行组件"""
    
    def __init__(self, key: str, value: str, parent=None):
        super().__init__(parent)
        self.init_ui(key, value)
    
    def init_ui(self, key: str, value: str):
        """初始化界面"""
        self.setFrameStyle(QFrame.Shape.NoFrame)
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 2, 5, 2)
        
        # 键标签
        key_label = QLabel(f"{key}:")
        key_label.setStyleSheet("""
            QLabel {
                color: #666666;
                font-weight: bold;
                min-width: 120px;
                max-width: 120px;
            }
        """)
        
        # 值标签
        value_label = QLabel(str(value))
        value_label.setWordWrap(True)
        value_label.setStyleSheet("""
            QLabel {
                color: #333333;
                padding-left: 10px;
            }
        """)
        
        layout.addWidget(key_label)
        layout.addWidget(value_label, 1)


class HardwareCard(QFrame):
    """硬件信息卡片组件"""
    
    # 信号定义
    copy_requested = Signal(str)  # 复制请求
    export_requested = Signal(str, dict)  # 导出请求 (标题, 数据)
    
    def __init__(self, title: str, icon: str, data: Dict[str, Any], parent=None):
        super().__init__(parent)
        self.title = title
        self.icon = icon
        self.data = data
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setFrameStyle(QFrame.Shape.Box)
        self.setStyleSheet("""
            QFrame {
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                background-color: #FFFFFF;
                margin: 5px;
            }
            QFrame:hover {
                border-color: #0078D4;
                box-shadow: 0 2px 8px rgba(0, 120, 212, 0.2);
            }
        """)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(10)
        
        # 标题栏
        header_layout = self.create_header()
        main_layout.addLayout(header_layout)
        
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setStyleSheet("QFrame { color: #E0E0E0; }")
        main_layout.addWidget(separator)
        
        # 内容区域
        content_widget = self.create_content()
        main_layout.addWidget(content_widget, 1)
        
        # 操作按钮
        button_layout = self.create_buttons()
        main_layout.addLayout(button_layout)
    
    def create_header(self) -> QHBoxLayout:
        """创建标题栏"""
        header_layout = QHBoxLayout()
        
        # 图标和标题
        title_label = QLabel(f"{self.icon} {self.title}")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("QLabel { color: #333333; }")
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        return header_layout
    
    def create_content(self) -> QScrollArea:
        """创建内容区域"""
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameStyle(QFrame.Shape.NoFrame)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                border: none;
                background: #F0F0F0;
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background: #C0C0C0;
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: #A0A0A0;
            }
        """)
        
        # 内容容器
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(2)
        
        # 添加信息行
        self.add_info_rows(content_layout, self.data)
        
        # 添加弹性空间
        content_layout.addStretch()
        
        scroll_area.setWidget(content_widget)
        return scroll_area
    
    def add_info_rows(self, layout: QVBoxLayout, data: Dict[str, Any], prefix: str = ""):
        """递归添加信息行"""
        for key, value in data.items():
            if key in ['error', 'traceback']:
                continue  # 跳过错误信息
            
            display_key = f"{prefix}{key}" if prefix else key
            
            if isinstance(value, dict):
                # 如果是字典，添加分组标题
                if value:  # 只有非空字典才显示
                    group_label = QLabel(f"📁 {display_key.replace('_', ' ').title()}")
                    group_label.setStyleSheet("""
                        QLabel {
                            color: #0078D4;
                            font-weight: bold;
                            margin-top: 10px;
                            margin-bottom: 5px;
                        }
                    """)
                    layout.addWidget(group_label)
                    
                    # 递归添加子项
                    self.add_info_rows(layout, value, "  ")
            elif isinstance(value, list):
                # 如果是列表，逐个显示
                if value:  # 只有非空列表才显示
                    group_label = QLabel(f"📋 {display_key.replace('_', ' ').title()}")
                    group_label.setStyleSheet("""
                        QLabel {
                            color: #0078D4;
                            font-weight: bold;
                            margin-top: 10px;
                            margin-bottom: 5px;
                        }
                    """)
                    layout.addWidget(group_label)
                    
                    for i, item in enumerate(value):
                        if isinstance(item, dict):
                            item_label = QLabel(f"  项目 {i + 1}:")
                            item_label.setStyleSheet("QLabel { color: #666666; font-weight: bold; }")
                            layout.addWidget(item_label)
                            self.add_info_rows(layout, item, "    ")
                        else:
                            info_row = InfoRow(f"项目 {i + 1}", str(item))
                            layout.addWidget(info_row)
            else:
                # 普通键值对
                formatted_key = display_key.replace('_', ' ').title()
                info_row = InfoRow(formatted_key, str(value))
                layout.addWidget(info_row)
    
    def create_buttons(self) -> QHBoxLayout:
        """创建操作按钮"""
        button_layout = QHBoxLayout()
        
        # 复制按钮
        copy_button = QPushButton("📋 复制信息")
        copy_button.setStyleSheet("""
            QPushButton {
                background-color: #F3F2F1;
                border: 1px solid #D1D1D1;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #E1DFDD;
                border-color: #0078D4;
            }
            QPushButton:pressed {
                background-color: #D1CFCD;
            }
        """)
        copy_button.clicked.connect(self.copy_info)
        
        # 导出按钮
        export_button = QPushButton("💾 导出数据")
        export_button.setStyleSheet("""
            QPushButton {
                background-color: #0078D4;
                border: 1px solid #0078D4;
                border-radius: 4px;
                padding: 6px 12px;
                color: white;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #106EBE;
                border-color: #106EBE;
            }
            QPushButton:pressed {
                background-color: #005A9E;
            }
        """)
        export_button.clicked.connect(self.export_info)
        
        button_layout.addStretch()
        button_layout.addWidget(copy_button)
        button_layout.addWidget(export_button)
        
        return button_layout
    
    def copy_info(self):
        """复制信息到剪贴板"""
        try:
            # 格式化数据为文本
            text_data = self.format_data_as_text(self.data)
            full_text = f"{self.icon} {self.title}\n{'=' * 50}\n{text_data}"
            
            # 复制到剪贴板
            clipboard = QApplication.clipboard()
            clipboard.setText(full_text)
            
            # 发送信号
            self.copy_requested.emit(f"{self.title}信息已复制到剪贴板")
            
        except Exception as e:
            QMessageBox.warning(self, "复制失败", f"复制信息时发生错误：{str(e)}")
    
    def export_info(self):
        """导出信息到文件"""
        try:
            # 选择保存文件
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                f"导出{self.title}信息",
                f"{self.title}_info.json",
                "JSON文件 (*.json);;文本文件 (*.txt)"
            )
            
            if file_path:
                if file_path.endswith('.json'):
                    # 导出为JSON
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump({
                            "title": self.title,
                            "export_time": __import__('datetime').datetime.now().isoformat(),
                            "data": self.data
                        }, f, indent=4, ensure_ascii=False)
                else:
                    # 导出为文本
                    text_data = self.format_data_as_text(self.data)
                    full_text = f"{self.icon} {self.title}\n{'=' * 50}\n{text_data}"
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(full_text)
                
                # 发送信号
                self.export_requested.emit(f"{self.title}信息已导出到 {file_path}", self.data)
                
        except Exception as e:
            QMessageBox.warning(self, "导出失败", f"导出信息时发生错误：{str(e)}")
    
    def format_data_as_text(self, data: Dict[str, Any], indent: int = 0) -> str:
        """将数据格式化为文本"""
        lines = []
        prefix = "  " * indent
        
        for key, value in data.items():
            if key in ['error', 'traceback']:
                continue
            
            formatted_key = key.replace('_', ' ').title()
            
            if isinstance(value, dict):
                if value:
                    lines.append(f"{prefix}{formatted_key}:")
                    lines.append(self.format_data_as_text(value, indent + 1))
            elif isinstance(value, list):
                if value:
                    lines.append(f"{prefix}{formatted_key}:")
                    for i, item in enumerate(value):
                        if isinstance(item, dict):
                            lines.append(f"{prefix}  项目 {i + 1}:")
                            lines.append(self.format_data_as_text(item, indent + 2))
                        else:
                            lines.append(f"{prefix}  - {item}")
            else:
                lines.append(f"{prefix}{formatted_key}: {value}")
        
        return "\n".join(lines)
    
    def update_data(self, new_data: Dict[str, Any]):
        """更新卡片数据"""
        self.data = new_data
        # 重新创建内容区域
        # 这里可以优化为只更新变化的部分
        self.init_ui()


if __name__ == "__main__":
    # 测试代码
    import sys
    
    app = QApplication(sys.argv)
    
    # 测试数据
    test_data = {
        "name": "Intel Core i7-14700KF",
        "manufacturer": "GenuineIntel",
        "cores": 20,
        "threads": 28,
        "base_clock": "3.4 GHz",
        "boost_clock": "5.6 GHz",
        "cache": {
            "l1": "1.25 MB",
            "l2": "28 MB",
            "l3": "33 MB"
        },
        "features": ["AVX2", "SSE4.2", "Hyper-Threading"]
    }
    
    # 创建测试窗口
    window = QWidget()
    window.setWindowTitle("硬件卡片测试")
    window.resize(400, 600)
    
    layout = QVBoxLayout(window)
    
    # 创建硬件卡片
    card = HardwareCard("CPU信息", "🖥️", test_data)
    layout.addWidget(card)
    
    window.show()
    
    sys.exit(app.exec())
